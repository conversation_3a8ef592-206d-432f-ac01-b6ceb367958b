/* Compact Observation Widget Styles */

.grouped-obs {
    line-height: 1.2 !important;
    font-size: 11px !important;
}

.grouped-obs .form-section {
    margin-bottom: 2px !important;
    padding: 2px 0 !important;
}

.grouped-obs .form-column {
    padding: 0 5px !important;
    margin-bottom: 2px !important;
}

.grouped-obs .control-input {
    margin-bottom: 2px !important;
}

.grouped-obs .form-group {
    margin-bottom: 2px !important;
}

.grouped-obs .control-label {
    margin-bottom: 1px !important;
    font-size: 10px !important;
}

.grouped-obs .control-input-wrapper {
    margin-bottom: 2px !important;
}

.grouped-obs .form-control {
    padding: 2px 6px !important;
    font-size: 10px !important;
    height: auto !important;
    min-height: 24px !important;
}

.grouped-obs .btn {
    padding: 2px 8px !important;
    font-size: 10px !important;
    line-height: 1.2 !important;
}

.grouped-obs .text-muted {
    font-size: 9px !important;
    line-height: 1.1 !important;
}

/* Remove extra spacing from section breaks */
.grouped-obs .section-head {
    margin-bottom: 2px !important;
    padding-bottom: 2px !important;
}

/* Compact the observation name and links */
.grouped-obs .observation-name {
    line-height: 1.1 !important;
    margin-bottom: 2px !important;
}

.grouped-obs .observation-name a {
    font-size: 10px !important;
    line-height: 1.1 !important;
}

/* Make the approve/disapprove buttons more compact */
.grouped-obs .btn-xs {
    padding: 1px 6px !important;
    font-size: 9px !important;
    line-height: 1.1 !important;
}

/* Reduce spacing in HTML fields */
.grouped-obs .control-input .form-control[data-fieldtype="HTML"] {
    padding: 0 !important;
    margin: 0 !important;
}

/* Make the note button more compact */
.grouped-obs .add-note-observation-btn {
    padding: 1px 3px !important;
    margin: 0 !important;
}

/* Compact the result input fields */
.grouped-obs input[data-fieldtype="Data"],
.grouped-obs select[data-fieldtype="Select"] {
    padding: 2px 4px !important;
    font-size: 10px !important;
    height: 22px !important;
}

/* Remove extra margins from divs */
.grouped-obs > div {
    margin-bottom: 1px !important;
}

/* Compact the overall container */
.observs {
    padding: 2px 5px !important;
    margin: 1px 5px 1px 0 !important;
}
